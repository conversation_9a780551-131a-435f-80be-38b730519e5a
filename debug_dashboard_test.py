#!/usr/bin/env python3
"""
Debug Test für Revolutionary Bitcoin Trading Dashboard
"""

import sys
import traceback

def debug_test():
    print("=" * 60)
    print("DEBUG TEST - REVOLUTIONARY BITCOIN TRADING DASHBOARD")
    print("=" * 60)
    
    try:
        print("1. Testing Python environment...")
        print(f"   Python version: {sys.version}")
        print("   ✅ Python OK")
        
        print("\n2. Testing imports...")
        
        print("   Testing tkinter...")
        import tkinter as tk
        print("   ✅ tkinter OK")
        
        print("   Testing pandas...")
        import pandas as pd
        print("   ✅ pandas OK")
        
        print("   Testing numpy...")
        import numpy as np
        print("   ✅ numpy OK")
        
        print("   Testing matplotlib...")
        import matplotlib
        matplotlib.use('TkAgg')
        import matplotlib.pyplot as plt
        print("   ✅ matplotlib OK")
        
        print("   Testing sklearn...")
        from sklearn.ensemble import RandomForestRegressor
        print("   ✅ sklearn OK")
        
        print("\n3. Testing dashboard import...")
        import REVOLUTIONARY_BITCOIN_TRADING_DASHBOARD
        print("   ✅ Dashboard import OK")
        
        print("\n4. Testing dashboard initialization...")
        dashboard = REVOLUTIONARY_BITCOIN_TRADING_DASHBOARD.RevolutionaryTradingDashboard()
        print("   ✅ Dashboard initialization OK")
        
        print("\n5. Testing GUI creation...")
        if hasattr(dashboard, 'root') and dashboard.root:
            print("   ✅ GUI root window created")
            
            # Test window without showing
            dashboard.root.withdraw()  # Hide window
            dashboard.root.update()    # Process events
            
            print("   ✅ GUI test successful")
            
            # Cleanup
            dashboard.root.destroy()
            print("   ✅ GUI cleanup successful")
        else:
            print("   ❌ GUI root window not found")
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS PASSED - DASHBOARD IS READY!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR during debug test:")
        print(f"   Error: {e}")
        print(f"   Type: {type(e).__name__}")
        print("\nFull traceback:")
        traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("❌ DEBUG TEST FAILED")
        print("=" * 60)
        
        return False

if __name__ == "__main__":
    debug_test()
